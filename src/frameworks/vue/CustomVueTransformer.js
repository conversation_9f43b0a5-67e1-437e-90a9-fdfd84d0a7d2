/**
 * Vue 转换器
 * 提供自定义的 Vue 2 到 Vue 3 代码转换规则
 */
class CustomVueTransformer {
  constructor(options = {}) {
    this.options = options;
  }

  async transform(code, filePath = '') {
    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    // 处理其他可能的 gogocodeTransfer 导入格式
    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\.\/\.\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    // 处理更复杂的相对路径
    code = code.replace(
      /import\s+{\s*\$on,\s*\$off,\s*\$once,\s*\$emit\s*}\s+from\s+['"]\.\.\/\.\.\/\.\.\/utils\/gogocodeTransfer['"]/g,
      'import { $on, $off, $once, $emit } from \'@/utils/gogocodeTransfer\''
    )

    // 替换 Element UI 导入
    code = code.replace(
      /import\s+{\s*([^}]+)\s*}\s+from\s+['"]element-ui['\"]/g,
      (match, imports) => {
        // 清理导入列表中的多余空格
        const cleanImports = imports.replace(/\s+/g, ' ').trim()
        return `import { ${cleanImports} } from 'element-plus'`
      }
    )

    // 替换 Element UI 完整导入
    code = code.replace(
      /import\s+ElementUI\s+from\s+['"]element-ui['\"]/g,
      'import ElementPlus from \'element-plus\''
    )

    // 替换 Element UI CSS 导入
    code = code.replace(
      /import\s+['"]element-ui\/lib\/theme-chalk\/index\.css['\"]/g,
      'import \'element-plus/dist/index.css\''
    )

    // 替换 Vue 导入 - 关键转换
    code = code.replace(
      /import\s+Vue\s+from\s+['"]vue['\"]/g,
      'import { createApp } from \'vue\''
    )

    // require('element-ui/package.json') to require('element-plus/package.json')
    code = code.replace(
      /require\(\s*['"]element-ui\/package\.json['\"]\s*\)/g,
      'require(\'element-plus/package.json\')'
    )

    // replace 'element-ui/lib/locale/lang/en' to 'element-plus/es/locale/lang/en'
    code = code.replace(
      'element-ui/lib/locale/lang/',
      'element-plus/es/locale/lang/'
    )

    // 修复多行 @click 事件处理器语法问题
    code = this.fixMultilineClickHandlers(code);

    return code
  }

  /**
   * 修复多行 @click 事件处理器语法问题
   * 将多行语句转换为箭头函数格式
   * @param {string} code - Vue 文件代码
   * @returns {string} 转换后的代码
   */
  fixMultilineClickHandlers(code) {
    // 匹配多行 @click 事件处理器，使用更精确的正则表达式
    // 匹配 @click=" 开始，到下一个 " 结束的内容，支持跨行
    const multilineClickRegex = /@click="([\s\S]*?)"/g;

    return code.replace(multilineClickRegex, (match, clickContent) => {
      // 清理内容，移除首尾空白字符
      const cleanContent = clickContent.trim();

      // 检查是否已经是函数格式
      // 更精确的检测：检查是否以箭头函数或function开头
      const trimmedContent = cleanContent.replace(/\s+/g, ' ').trim();
      if (trimmedContent.startsWith('()') ||
          trimmedContent.startsWith('(') && trimmedContent.includes(') =>') ||
          trimmedContent.startsWith('function')) {
        return match; // 已经是函数格式，不需要转换
      }

      // 检查是否是单行简单表达式（不包含换行符）
      if (!cleanContent.includes('\n')) {
        return match; // 单行表达式，不需要转换
      }

      // 将多行语句转换为箭头函数
      // 分割成多个语句，处理嵌套的函数调用
      const lines = cleanContent.split('\n').map(line => line.trim()).filter(line => line.length > 0);

      // 重新组装代码，保持原有的结构但确保语法正确
      let processedLines = [];
      let currentStatement = '';
      let braceCount = 0;

      for (const line of lines) {
        // 计算大括号的嵌套层级
        for (const char of line) {
          if (char === '{') braceCount++;
          if (char === '}') braceCount--;
        }

        currentStatement += (currentStatement ? ' ' : '') + line;

        // 如果大括号平衡且不是以逗号结尾，则认为是一个完整的语句
        if (braceCount === 0 && !line.endsWith(',')) {
          // 确保语句以分号结尾（除非已经有分号或是大括号结尾）
          if (!currentStatement.endsWith(';') && !currentStatement.endsWith('}')) {
            currentStatement += ';';
          }
          processedLines.push(currentStatement);
          currentStatement = '';
        }
      }

      // 处理剩余的语句
      if (currentStatement.trim()) {
        if (!currentStatement.endsWith(';') && !currentStatement.endsWith('}')) {
          currentStatement += ';';
        }
        processedLines.push(currentStatement);
      }

      // 构建箭头函数
      const functionBody = processedLines.join(' ');
      return `@click="() => { ${functionBody} }"`;
    });
  }

  /**
   * 转换 SCSS 中的 @import 为 @use
   * @param {string} code - Vue 文件代码
   * @returns {string} 转换后的代码
   */
  transformScssImports(code) {
    // 匹配 <style lang="scss" scoped> 或 <style lang="scss"> 块
    const styleRegex = /<style\s+lang=["']scss["'](?:\s+scoped)?\s*>([\s\S]*?)<\/style>/gi

    return code.replace(styleRegex, (match, styleContent) => {
      let transformedStyle = styleContent

      // 转换 @import 为 @use，但需要智能判断转换方式
      transformedStyle = transformedStyle.replace(
        /@import\s+['"]([^'"]+)['"];?/g,
        (importMatch, path) => {
          // 如果是外部 URL（http/https）或 .css 文件，保持 @import
          if (path.match(/^https?:/) || path.endsWith('.css')) {
            return importMatch // 保持原样
          }

          // 根据路径类型决定转换方式
          const filename = path.split('/').pop().replace(/^_/, '') // 移除前缀下划线

          // 对于常见的基础文件（变量、混合器等），使用 as * 以保持兼容性
          const baseFiles = ['variables', 'mixins', 'functions', 'utils', 'helpers', 'base', 'reset', 'normalize']
          const isBaseFile = baseFiles.some(base => filename.startsWith(base))

          if (isBaseFile) {
            return `@use '${path}' as *;`
          } else {
            // 对于组件或其他模块，使用命名空间
            const namespace = filename.replace(/\.(scss|sass)$/, '')
            return `@use '${path}' as ${namespace};`
          }
        }
      )

      // 返回更新后的 style 标签
      return match.replace(styleContent, transformedStyle)
    })
  }
}

module.exports = CustomVueTransformer
