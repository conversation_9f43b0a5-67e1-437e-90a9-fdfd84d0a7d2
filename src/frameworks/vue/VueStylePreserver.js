const { tokenize, constructTree } = require('hyntax-yx');

/**
 * Vue Style 块保护器
 * 用于在 gogocode 转换前提取 style 块，转换后恢复，避免 SCSS 注释格式错乱
 *
 * https://github.com/thx/gogocode/issues/209
 */
class VueStylePreserver {
    constructor(options = {}) {
        this.options = {
            verbose: false,
            ...options
        };
    }

    /**
     * 提取 Vue 文件中的 style 块
     * @param {string} vueContent - Vue 文件内容
     * @returns {object} - { contentWithoutStyles: 去除style的内容, originalStyleBlocks: 原始样式块 }
     */
    extractStyleBlocks(vueContent) {
        try {
            const { tokens } = tokenize(vueContent);
            const { ast } = constructTree(tokens);

            const styleBlocks = [];

            // 查找所有 style 节点
            this.findStyleNodes(ast, styleBlocks);

            if (styleBlocks.length === 0) {
                return {
                    contentWithoutStyles: vueContent,
                    originalStyleBlocks: []
                };
            }

            // 按位置倒序排列，从后往前移除，避免位置偏移问题
            styleBlocks.sort((a, b) => b.startPosition - a.startPosition);

            let contentWithoutStyles = vueContent;

            // 移除所有 style 块
            styleBlocks.forEach((styleBlock, index) => {
                const start = styleBlock.startPosition;
                const end = styleBlock.endPosition;

                contentWithoutStyles = contentWithoutStyles.slice(0, start) +
                                     contentWithoutStyles.slice(end);
            });

            return {
                contentWithoutStyles: contentWithoutStyles,
                originalStyleBlocks: styleBlocks
            };
        } catch (error) {
            if (this.options.verbose) {
                console.warn(`提取 style 块失败: ${error.message}`);
            }
            // 如果解析失败，返回原内容
            return {
                contentWithoutStyles: vueContent,
                originalStyleBlocks: []
            };
        }
    }

    /**
     * 恢复 style 块到转换后的内容中
     * @param {string} transformedContent - 转换后的内容
     * @param {Array} originalStyleBlocks - 原始样式块
     * @returns {string} - 恢复后的内容
     */
    restoreStyleBlocks(transformedContent, originalStyleBlocks) {
        if (!originalStyleBlocks || originalStyleBlocks.length === 0) {
            return transformedContent;
        }

        // 查找转换后内容中的 style 块位置
        const transformedStyleRegex = /<style[^>]*>[\s\S]*?<\/style>/gi;
        const transformedStyleMatches = [];
        let match;

        while ((match = transformedStyleRegex.exec(transformedContent)) !== null) {
            transformedStyleMatches.push({
                content: match[0],
                startIndex: match.index,
                endIndex: match.index + match[0].length
            });
        }

        // 如果转换后的 style 块数量与原始数量相同，则替换
        if (transformedStyleMatches.length === originalStyleBlocks.length) {
            let restoredContent = transformedContent;

            // 从后往前替换，避免位置偏移
            for (let i = transformedStyleMatches.length - 1; i >= 0; i--) {
                const transformedMatch = transformedStyleMatches[i];
                const originalStyleBlock = originalStyleBlocks[i];

                restoredContent = restoredContent.slice(0, transformedMatch.startIndex) +
                                originalStyleBlock.content +
                                restoredContent.slice(transformedMatch.endIndex);
            }

            return restoredContent;
        } else {
            let restoredContent = transformedContent;
            originalStyleBlocks.forEach((styleBlock, index) => {
                restoredContent += '\n\n' + styleBlock.content;
            });

            return restoredContent;
        }
    }

    /**
     * 递归查找 AST 中的 style 节点
     * @param {object} node - AST 节点
     * @param {Array} styleBlocks - 收集的样式块数组
     */
    findStyleNodes(node, styleBlocks) {
        if (!node) return;

        // 检查当前节点是否是 style 节点
        if (node.nodeType === 'style' && node.content) {
            const styleBlock = this.extractStyleNodeContent(node);
            if (styleBlock) {
                styleBlocks.push(styleBlock);
            }
        }

        // 递归检查子节点
        if (node.content && node.content.children) {
            node.content.children.forEach(child => {
                this.findStyleNodes(child, styleBlocks);
            });
        }
    }

    /**
     * 从 style 节点提取内容信息
     * @param {object} styleNode - style AST 节点
     * @returns {object} - 样式块信息
     */
    extractStyleNodeContent(styleNode) {
        try {
            const content = styleNode.content;
            if (!content.openStart || !content.close) {
                return null;
            }

            const startPosition = content.openStart.startPosition;
            // 确保包含完整的结束标签，包括最后的 '>'
            const endPosition = content.close.startPosition + content.close.content.length;

            // 构建完整的 style 标签内容
            let styleContent = '';

            // 开始标签
            styleContent += content.openStart.content;

            // 属性
            if (content.attributes && content.attributes.length > 0) {
                content.attributes.forEach(attr => {
                    if (attr.key) {
                        styleContent += ' ' + attr.key.content;
                    }
                    if (attr.startWrapper) {
                        styleContent += '=' + attr.startWrapper.content;
                    }
                    if (attr.value) {
                        styleContent += attr.value.content;
                    }
                    if (attr.endWrapper) {
                        styleContent += attr.endWrapper.content;
                    }
                });
            }

            // 结束开始标签
            if (content.openEnd) {
                styleContent += content.openEnd.content;
            }

            // style 内容
            if (content.value) {
                styleContent += content.value.content;
            }

            // 关闭标签
            styleContent += content.close.content;

            return {
                startPosition,
                endPosition,
                content: styleContent
            };
        } catch (error) {
            if (this.options.verbose) {
                console.warn(`提取 style 节点内容失败: ${error.message}`);
            }
            return null;
        }
    }



    /**
     * 处理 Vue 文件：提取 style 块 -> 转换 -> 恢复 style 块
     * @param {string} vueContent - Vue 文件内容
     * @param {Function} transformFunction - 转换函数
     * @param {...any} transformArgs - 转换函数的参数
     * @returns {string} - 处理后的内容
     */
    async processVueFile(vueContent, transformFunction, ...transformArgs) {
        try {
            // 1. 提取 style 块
            const { contentWithoutStyles, originalStyleBlocks } = this.extractStyleBlocks(vueContent);

            // 2. 如果没有 style 块，直接进行转换
            if (originalStyleBlocks.length === 0) {
                return await transformFunction(vueContent, ...transformArgs);
            }

            // 3. 对没有 style 块的内容进行转换
            const transformedContent = await transformFunction(contentWithoutStyles, ...transformArgs);

            // 4. 恢复 style 块
            const finalContent = this.restoreStyleBlocks(transformedContent, originalStyleBlocks);
            return finalContent;
        } catch (error) {
            if (this.options.verbose) {
                console.warn(`处理 Vue 文件失败: ${error.message}`);
            }
            // 如果处理失败，回退到原始转换
            return await transformFunction(vueContent, ...transformArgs);
        }
    }
}

module.exports = VueStylePreserver;
