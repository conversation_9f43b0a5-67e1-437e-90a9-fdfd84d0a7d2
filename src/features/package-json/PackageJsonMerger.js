const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const ComponentDependencyMapper = require('../../frameworks/vue/third-party/ComponentDependencyMapper');

/**
 * Package.json 处理器
 * 负责智能合并源项目和目标项目的 package.json，以及升级单个项目的依赖
 * 合并了原来的 PackageJsonMerger 和 PackageUpgrader 功能
 */
class PackageJsonMerger {
  constructor(sourceProjectPath, targetProjectPath, options = {}) {
    // 支持两种模式：合并模式和升级模式
    if (typeof sourceProjectPath === 'object') {
      // 升级模式：PackageJsonMerger(options)
      options = sourceProjectPath;
      this.mode = 'upgrade';
      this.projectPath = options.projectPath || options.workingPath;
      this.packageJsonPath = path.join(this.projectPath, 'package.json');
    } else {
      // 合并模式：PackageJsonMerger(sourcePath, targetPath, options)
      this.mode = 'merge';
      this.sourceProjectPath = sourceProjectPath;
      this.targetProjectPath = targetProjectPath;
    }

    this.options = {
      // 合并模式选项
      preserveTargetDependencies: true, // 保留目标项目的现有依赖
      enableThirdPartyMapping: true, // 启用第三方组件映射

      // 升级模式选项
      migrationMode: false, // 是否为迁移模式（从 Vue 2 到 Vue 3）
      preserveVue3Dependencies: true, // 是否保留已有的 Vue 3 依赖
      autoFixConfig: true, // 是否自动修复配置文件

      // 通用选项
      verbose: false,
      dryRun: false,
      ...options
    };

    // 初始化依赖映射器
    if (this.options.enableThirdPartyMapping) {
      this.dependencyMapper = new ComponentDependencyMapper();
    }

    // 加载配置
    this.configPath = path.join(__dirname, '../../../config/package-recommend.json');
    this.config = null;
    this.isVue3Project = false; // 用于升级模式

    // Vue 2 官方依赖列表（这些依赖在 Vue 3 中不需要）
    this.vue2OfficialDeps = new Set([
      'vue-template-compiler',
      '@vue/composition-api',
      'vue-class-component',
      'vue-property-decorator',
      'vuex-class'
    ]);
  }

  /**
   * 加载配置文件
   */
  async loadConfig() {
    try {
      this.config = await fs.readJson(this.configPath);

      // 如果是升级模式，检测 Vue 版本
      if (this.mode === 'upgrade') {
        await this.detectVueVersion();
      }
    } catch (error) {
      console.error(chalk.red('❌ 无法加载配置文件:'), error.message);
      throw new Error(`Failed to load config file: ${this.configPath}`);
    }
  }

  /**
   * 检测 Vue 版本（升级模式专用）
   */
  async detectVueVersion() {
    try {
      if (await fs.pathExists(this.packageJsonPath)) {
        const packageJson = await fs.readJson(this.packageJsonPath);
        const vueDep = packageJson.dependencies?.vue || packageJson.devDependencies?.vue;

        if (vueDep) {
          this.isVue3Project = vueDep.startsWith('^3') || vueDep.startsWith('3');
          if (this.options.verbose) {
            console.log(chalk.gray(`检测到 Vue 版本: ${vueDep} (Vue ${this.isVue3Project ? '3' : '2'} 项目)`));
          }
        }
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow('⚠️ 无法检测 Vue 版本，将按默认设置处理'));
      }
    }
  }

  /**
   * 主要处理方法 - 根据模式执行合并或升级
   */
  async process() {
    if (this.mode === 'merge') {
      return await this.merge();
    } else {
      return await this.upgrade();
    }
  }

  /**
   * 执行 package.json 合并
   */
  async merge() {
    console.log(chalk.blue('📦 开始合并 package.json...'));

    try {
      // 加载配置
      await this.loadConfig();

      // 读取源项目和目标项目的 package.json
      const sourcePackageJson = await this.readPackageJson(this.sourceProjectPath);
      const targetPackageJson = await this.readPackageJson(this.targetProjectPath);

      if (!sourcePackageJson) {
        throw new Error(`源项目中未找到 package.json: ${this.sourceProjectPath}`);
      }

      if (!targetPackageJson) {
        throw new Error(`目标项目中未找到 package.json: ${this.targetProjectPath}`);
      }

      // 执行智能合并
      const mergedPackageJson = await this.performIntelligentMerge(sourcePackageJson, targetPackageJson);

      // 写入合并后的 package.json
      if (!this.options.dryRun) {
        const targetPackageJsonPath = path.join(this.targetProjectPath, 'package.json');
        await fs.writeJson(targetPackageJsonPath, mergedPackageJson, { spaces: 2 });
      }

      console.log(chalk.green('✅ package.json 合并完成!'));

      return {
        success: true,
        mergedPackageJson: this.options.dryRun ? mergedPackageJson : null,
        changes: this.generateChangesSummary(sourcePackageJson, targetPackageJson, mergedPackageJson)
      };
    } catch (error) {
      console.error(chalk.red('❌ package.json 合并失败:'), error.message);
      throw error;
    }
  }

  /**
   * 执行 package.json 升级
   */
  async upgrade() {
    console.log(chalk.blue('📦 开始升级 package.json 依赖...'));

    try {
      // 加载配置文件
      await this.loadConfig();

      // 检查 package.json 是否存在
      if (!await fs.pathExists(this.packageJsonPath)) {
        throw new Error(`package.json 不存在: ${this.packageJsonPath}`);
      }

      // 读取 package.json
      const packageJson = await fs.readJson(this.packageJsonPath);

      // 升级依赖
      const result = this.upgradeDependencies(packageJson);

      // 写入更新后的 package.json
      if (!this.options.dryRun) {
        await fs.writeJson(this.packageJsonPath, packageJson, { spaces: 2 });
      }

      console.log(chalk.green('✅ package.json 依赖升级完成!'));
      this.printUpgradeResult(result);

      return {
        success: true,
        ...result
      };
    } catch (error) {
      console.error(chalk.red('❌ package.json 依赖升级失败:'), error.message);
      throw error;
    }
  }

  /**
   * 读取 package.json 文件
   */
  async readPackageJson(projectPath) {
    try {
      const packageJsonPath = path.join(projectPath, 'package.json');
      if (await fs.pathExists(packageJsonPath)) {
        return await fs.readJson(packageJsonPath);
      }
      return null;
    } catch (error) {
      console.warn(chalk.yellow(`读取 package.json 失败: ${error.message}`));
      return null;
    }
  }

  /**
   * 执行智能合并
   */
  async performIntelligentMerge(sourcePackageJson, targetPackageJson) {
    // 以目标项目的 package.json 为基础
    const mergedPackageJson = JSON.parse(JSON.stringify(targetPackageJson));

    // 合并基本信息（保留目标项目的信息，但可以从源项目补充缺失的字段）
    this.mergeBasicInfo(sourcePackageJson, mergedPackageJson);

    // 合并 scripts（智能合并，避免覆盖重要的构建脚本）
    this.mergeScripts(sourcePackageJson, mergedPackageJson);

    // 合并依赖（最复杂的部分）
    await this.mergeDependencies(sourcePackageJson, mergedPackageJson);

    // 合并其他字段
    this.mergeOtherFields(sourcePackageJson, mergedPackageJson);

    return mergedPackageJson;
  }

  /**
   * 合并基本信息
   */
  mergeBasicInfo(sourcePackageJson, mergedPackageJson) {
    // 保留目标项目的 name, version, description
    // 但如果目标项目缺少这些字段，可以从源项目补充
    const fieldsToMerge = ['description', 'keywords', 'author', 'license', 'repository', 'bugs', 'homepage'];

    fieldsToMerge.forEach(field => {
      if (!mergedPackageJson[field] && sourcePackageJson[field]) {
        mergedPackageJson[field] = sourcePackageJson[field];
        if (this.options.verbose) {
          console.log(chalk.gray(`  补充字段 ${field}: ${JSON.stringify(sourcePackageJson[field])}`));
        }
      }
    });
  }

  /**
   * 合并 scripts
   */
  mergeScripts(sourcePackageJson, mergedPackageJson) {
    if (!sourcePackageJson.scripts) return;

    if (!mergedPackageJson.scripts) {
      mergedPackageJson.scripts = {};
    }

    // 保留目标项目的重要脚本，但可以从源项目添加缺失的脚本
    const importantScripts = new Set(['serve', 'build', 'dev', 'start', 'test', 'lint']);

    Object.entries(sourcePackageJson.scripts).forEach(([scriptName, scriptCommand]) => {
      if (!mergedPackageJson.scripts[scriptName]) {
        // 如果目标项目没有这个脚本，添加它
        mergedPackageJson.scripts[scriptName] = scriptCommand;
        if (this.options.verbose) {
          console.log(chalk.gray(`  添加脚本 ${scriptName}: ${scriptCommand}`));
        }
      } else if (!importantScripts.has(scriptName)) {
        // 如果不是重要脚本，可以考虑覆盖
        if (this.options.verbose) {
          console.log(chalk.gray(`  保留目标项目的脚本 ${scriptName}`));
        }
      }
    });
  }

  /**
   * 合并依赖（核心逻辑）
   */
  async mergeDependencies(sourcePackageJson, mergedPackageJson) {
    console.log(chalk.gray('  🔄 智能合并依赖...'));

    // 处理 dependencies
    if (sourcePackageJson.dependencies) {
      await this.mergeDependencySection(
        sourcePackageJson.dependencies,
        mergedPackageJson,
        'dependencies'
      );
    }

    // 处理 devDependencies
    if (sourcePackageJson.devDependencies) {
      await this.mergeDependencySection(
        sourcePackageJson.devDependencies,
        mergedPackageJson,
        'devDependencies'
      );
    }
  }

  /**
   * 合并依赖部分
   */
  async mergeDependencySection(sourceDeps, mergedPackageJson, section) {
    if (!mergedPackageJson[section]) {
      mergedPackageJson[section] = {};
    }

    const targetDeps = mergedPackageJson[section];
    const changes = [];

    for (const [depName, depVersion] of Object.entries(sourceDeps)) {
      // 跳过 Vue 2 官方依赖
      if (this.vue2OfficialDeps.has(depName)) {
        if (this.options.verbose) {
          console.log(chalk.gray(`  跳过 Vue 2 官方依赖: ${depName}`));
        }
        continue;
      }

      // 检查是否需要进行第三方组件映射
      if (this.options.enableThirdPartyMapping && this.dependencyMapper.hasMapping(depName)) {
        const mapping = this.dependencyMapper.getMigrationInfo(depName);
        const targetDepName = mapping.target;

        if (!targetDeps[targetDepName]) {
          targetDeps[targetDepName] = 'latest'; // 或者从映射中获取推荐版本

          // 构建更详细的变更信息
          const difficultyEmoji = {
            'easy': '🟢',
            'medium': '🟡',
            'hard': '🔴'
          };

          const changeMsg = `映射 ${depName} -> ${targetDepName}`;
          const detailMsg = mapping.difficulty ?
            `${changeMsg} (${difficultyEmoji[mapping.difficulty] || '⚪'} ${mapping.difficulty})` :
            changeMsg;

          changes.push(detailMsg);

          if (this.options.verbose) {
            console.log(chalk.green(`  映射依赖: ${depName} -> ${targetDepName}`));
            if (mapping.difficulty) {
              console.log(chalk.gray(`    难度: ${difficultyEmoji[mapping.difficulty] || '⚪'} ${mapping.difficulty}`));
            }
            if (mapping.migrationType) {
              console.log(chalk.gray(`    类型: ${mapping.migrationType}`));
            }
            if (mapping.breakingChanges && mapping.breakingChanges.length > 0) {
              console.log(chalk.yellow(`    ⚠️  有 ${mapping.breakingChanges.length} 项破坏性变更`));
            }
          }
        }
        continue;
      }

      // 检查是否在已知不兼容列表中
      if (this.config.knownIncompatible[depName]) {
        const incompatible = this.config.knownIncompatible[depName];
        if (incompatible.alternatives && incompatible.alternatives.length > 0) {
          const alternative = incompatible.alternatives[0];
          if (!targetDeps[alternative] && this.config.knownCompatible[alternative]) {
            targetDeps[alternative] = this.config.knownCompatible[alternative].version || 'latest';
            changes.push(`替换不兼容依赖 ${depName} -> ${alternative}`);
            if (this.options.verbose) {
              console.log(chalk.yellow(`  替换不兼容依赖: ${depName} -> ${alternative}`));
            }
          }
        }
        continue;
      }

      // 检查目标项目是否已有此依赖
      if (targetDeps[depName]) {
        if (this.options.preserveTargetDependencies) {
          // 保留目标项目的版本
          if (this.options.verbose) {
            console.log(chalk.gray(`  保留目标依赖: ${depName}@${targetDeps[depName]}`));
          }
        } else {
          // 使用源项目的版本
          targetDeps[depName] = depVersion;
          changes.push(`更新 ${depName}: ${targetDeps[depName]} -> ${depVersion}`);
        }
      } else {
        // 目标项目没有此依赖，检查是否应该添加
        if (this.shouldAddDependency(depName, depVersion)) {
          targetDeps[depName] = depVersion;
          changes.push(`添加 ${depName}@${depVersion}`);
          if (this.options.verbose) {
            console.log(chalk.green(`  添加依赖: ${depName}@${depVersion}`));
          }
        }
      }
    }

    return changes;
  }

  /**
   * 判断是否应该添加依赖
   */
  shouldAddDependency(depName, depVersion) {
    // 检查是否在已知兼容列表中
    if (this.config.knownCompatible[depName]) {
      return true;
    }

    // 检查是否在需要升级列表中
    if (this.config.needsUpgrade[depName]) {
      return true;
    }

    // 检查是否在系统依赖列表中（通常不需要添加）
    if (this.config.systemDependencies.includes(depName)) {
      return false;
    }

    // 默认添加（但会在后续步骤中进行兼容性检查）
    return true;
  }

  /**
   * 合并其他字段
   */
  mergeOtherFields(sourcePackageJson, mergedPackageJson) {
    const fieldsToMerge = ['browserslist', 'engines', 'os', 'cpu'];

    fieldsToMerge.forEach(field => {
      if (sourcePackageJson[field] && !mergedPackageJson[field]) {
        mergedPackageJson[field] = sourcePackageJson[field];
        if (this.options.verbose) {
          console.log(chalk.gray(`  添加字段 ${field}`));
        }
      }
    });
  }

  /**
   * 生成变更摘要
   */
  generateChangesSummary(sourcePackageJson, targetPackageJson, mergedPackageJson) {
    const changes = [];

    // 比较依赖变化
    const sections = ['dependencies', 'devDependencies'];
    sections.forEach(section => {
      if (mergedPackageJson[section] && targetPackageJson[section]) {
        const merged = mergedPackageJson[section];
        const target = targetPackageJson[section];

        Object.keys(merged).forEach(depName => {
          if (!target[depName]) {
            changes.push(`添加 ${section}: ${depName}@${merged[depName]}`);
          } else if (target[depName] !== merged[depName]) {
            changes.push(`更新 ${section}: ${depName} ${target[depName]} -> ${merged[depName]}`);
          }
        });
      }
    });

    return changes;
  }

  /**
   * 升级依赖版本（升级模式专用）
   */
  upgradeDependencies(packageJson) {
    const result = {
      upgraded: [],
      added: [],
      removed: [],
      unchanged: []
    };

    // 在处理依赖之前，先保存原始依赖信息用于条件判断
    const originalDependencies = {
      ...packageJson.dependencies,
      ...packageJson.devDependencies
    };

    const dependencyMapping = this.getDependencyMapping();
    const dependenciesToRemove = this.getDependenciesToRemove();
    const newDependencies = this.getNewDependencies();

    // 处理 dependencies
    if (packageJson.dependencies) {
      this.processDependencies(packageJson.dependencies, result, 'dependencies', dependencyMapping, dependenciesToRemove);
    }

    // 处理 devDependencies
    if (packageJson.devDependencies) {
      this.processDependencies(packageJson.devDependencies, result, 'devDependencies', dependencyMapping, dependenciesToRemove);
    }

    // 添加新依赖（使用原始依赖信息进行条件判断）
    this.addNewDependencies(packageJson, result, newDependencies, originalDependencies);

    // 添加必要的 polyfill 依赖
    this.addRequiredPolyfills(packageJson, result);

    // 更新脚本
    this.updateScripts(packageJson);

    // 清理不需要的字段
    delete packageJson['engines'];

    return result;
  }

  /**
   * 处理依赖对象（升级模式专用）
   */
  processDependencies(deps, result, type, dependencyMapping, dependenciesToRemove) {
    Object.keys(deps).forEach(depName => {
      if (dependenciesToRemove.includes(depName)) {
        // 删除依赖
        delete deps[depName];
        result.removed.push({ name: depName, type });
      } else if (dependencyMapping[depName]) {
        // 智能处理依赖升级
        const oldVersion = deps[depName];
        const newVersion = dependencyMapping[depName];

        deps[depName] = newVersion;
        result.upgraded.push({
          name: depName,
          oldVersion,
          newVersion,
          type
        });
      } else {
        // 保持不变
        result.unchanged.push({ name: depName, version: deps[depName], type });
      }
    });
  }

  /**
   * 添加新依赖（升级模式专用）
   */
  addNewDependencies(packageJson, result, newDependencies, originalDependencies = null) {
    if (!packageJson.dependencies) {
      packageJson.dependencies = {};
    }

    // 使用原始依赖信息进行条件判断，如果没有提供则使用当前的
    const depsForConditionCheck = originalDependencies || {
      ...packageJson.dependencies,
      ...packageJson.devDependencies
    };

    // 获取完整的依赖列表，包括条件依赖
    const allPotentialDependencies = this.getAllPotentialDependencies(depsForConditionCheck, newDependencies);

    Object.entries(allPotentialDependencies).forEach(([depName, version]) => {
      // 检查依赖是否已存在
      const existsInDeps = packageJson.dependencies[depName];
      const existsInDevDeps = packageJson.devDependencies?.[depName];

      if (!existsInDeps && !existsInDevDeps) {
        // 检查是否应该添加这个依赖（使用原始依赖信息）
        if (this.shouldAddDependencyForUpgrade(depName, { dependencies: depsForConditionCheck, devDependencies: {} })) {
          packageJson.dependencies[depName] = version;
          result.added.push({ name: depName, version, type: 'dependencies' });
        } else {
          if (this.options.verbose) {
            console.log(chalk.gray(`  跳过添加 ${depName} (不满足添加条件)`));
          }
        }
      }
    });
  }

  /**
   * 获取所有可能的依赖（包括条件依赖）
   */
  getAllPotentialDependencies(dependenciesForCheck, baseDependencies) {
    const allDeps = { ...baseDependencies };

    // 添加条件依赖
    const conditionalDeps = {
      'element-plus': '^2.9.0',
      '@element-plus/icons-vue': '^2.3.1'
    };

    // 合并所有依赖
    return { ...allDeps, ...conditionalDeps };
  }

  /**
   * 获取需要添加的新依赖（升级模式专用）
   */
  getNewDependencies() {
    // 基础必需依赖
    const baseDependencies = {
      '@vue/compiler-sfc': '^3.4.0'
    };

    // 如果不是迁移模式或不保留 Vue 3 依赖，则添加所有默认依赖
    if (!this.isVue3Project || !this.options.preserveVue3Dependencies) {
      return {
        ...baseDependencies,
        'element-plus': '^2.9.0',
        '@element-plus/icons-vue': '^2.3.1'
      };
    }

    return baseDependencies;
  }

  /**
   * 获取依赖映射（升级模式专用）
   */
  getDependencyMapping() {
    const mapping = {};

    // 处理已知兼容的依赖
    Object.entries(this.config.knownCompatible).forEach(([depName, config]) => {
      mapping[depName] = config.version;
    });

    // 处理需要升级的依赖
    if (this.config.needsUpgrade) {
      Object.entries(this.config.needsUpgrade).forEach(([depName, config]) => {
        mapping[depName] = config.version;
        if (this.options.verbose) {
          console.log(chalk.yellow(`  标记需要升级: ${depName} -> ${config.version} (${config.note || config.description || ''})`));
        }
      });
    }

    return mapping;
  }

  /**
   * 获取需要移除的依赖（升级模式专用）
   */
  getDependenciesToRemove() {
    return Object.keys(this.config.knownIncompatible);
  }

  /**
   * 判断是否应该添加某个依赖（升级模式专用）
   */
  shouldAddDependencyForUpgrade(depName, packageJson) {
    // 对于基础依赖，总是添加
    const baseDependencies = ['@vue/compiler-sfc'];
    if (baseDependencies.includes(depName)) {
      return true;
    }

    // 如果是 Vue 3 项目且启用了依赖保护
    if (this.isVue3Project && this.options.preserveVue3Dependencies) {
      return this.isEssentialDependency(depName, packageJson);
    }

    // 其他情况下，检查是否是必要依赖
    return this.isEssentialDependency(depName, packageJson);
  }

  /**
   * 判断是否是必要依赖
   */
  isEssentialDependency(depName, packageJson) {
    // 基础必要依赖
    const essentialDeps = ['@vue/compiler-sfc', 'vue', 'vue-router'];
    if (essentialDeps.includes(depName)) {
      return true;
    }

    // 条件依赖检查
    if (depName === 'element-plus' || depName === '@element-plus/icons-vue') {
      const deps = packageJson.dependencies || {};
      const devDeps = packageJson.devDependencies || {};
      return deps['element-ui'] || devDeps['element-ui'] || deps['element-plus'] || devDeps['element-plus'];
    }

    return false;
  }

  /**
   * 添加必要的 polyfill 依赖
   */
  addRequiredPolyfills(packageJson, result) {
    // 从正确的配置路径获取 polyfill 依赖
    const polyfillConfig = this.config.migrationSettings?.requiredPolyfills;

    if (!polyfillConfig || !polyfillConfig.enabled) {
      if (this.options.verbose) {
        console.log(chalk.gray('  跳过 polyfill 添加（未启用或配置不存在）'));
      }
      return;
    }

    const polyfills = polyfillConfig.dependencies || {};

    Object.entries(polyfills).forEach(([polyfillName, config]) => {
      const version = config.version;
      const section = config.section || 'dependencies'; // 默认添加到 dependencies

      // 检查依赖是否已存在
      const existsInDeps = packageJson.dependencies?.[polyfillName];
      const existsInDevDeps = packageJson.devDependencies?.[polyfillName];

      if (!existsInDeps && !existsInDevDeps) {
        // 确保目标 section 存在
        if (!packageJson[section]) {
          packageJson[section] = {};
        }

        packageJson[section][polyfillName] = version;
        result.added.push({ name: polyfillName, version, type: section });

        if (this.options.verbose) {
          console.log(chalk.green(`  添加 polyfill: ${polyfillName}@${version} (${config.reason})`));
        }
      } else {
        if (this.options.verbose) {
          console.log(chalk.gray(`  跳过 polyfill ${polyfillName} (已存在)`));
        }
      }
    });
  }

  /**
   * 更新脚本
   */
  updateScripts(packageJson) {
    if (!packageJson.scripts) {
      packageJson.scripts = {};
    }

    // 确保有基本的 Vue 3 脚本
    const defaultScripts = {
      'serve': 'vue-cli-service serve',
      'build': 'vue-cli-service build',
      'lint': 'vue-cli-service lint'
    };

    Object.entries(defaultScripts).forEach(([scriptName, scriptCommand]) => {
      if (!packageJson.scripts[scriptName]) {
        packageJson.scripts[scriptName] = scriptCommand;
        if (this.options.verbose) {
          console.log(chalk.gray(`  添加脚本: ${scriptName}`));
        }
      }
    });
  }

  /**
   * 打印升级结果
   */
  printUpgradeResult(result) {
    if (result.upgraded.length > 0) {
      console.log(chalk.green(`\n📈 升级了 ${result.upgraded.length} 个依赖:`));
      result.upgraded.forEach(dep => {
        console.log(chalk.gray(`  ${dep.name}: ${dep.oldVersion} -> ${dep.newVersion}`));
      });
    }

    if (result.added.length > 0) {
      console.log(chalk.green(`\n➕ 添加了 ${result.added.length} 个依赖:`));
      result.added.forEach(dep => {
        console.log(chalk.gray(`  ${dep.name}@${dep.version}`));
      });
    }

    if (result.removed.length > 0) {
      console.log(chalk.red(`\n🗑️  移除了 ${result.removed.length} 个依赖:`));
      result.removed.forEach(dep => {
        console.log(chalk.gray(`  ${dep.name}`));
      });
    }

    if (this.options.verbose && result.unchanged.length > 0) {
      console.log(chalk.gray(`\n📋 保持不变的依赖 (${result.unchanged.length} 个):`));
      result.unchanged.slice(0, 10).forEach(dep => {
        console.log(chalk.gray(`  ${dep.name}@${dep.version}`));
      });
      if (result.unchanged.length > 10) {
        console.log(chalk.gray(`  ... 还有 ${result.unchanged.length - 10} 个`));
      }
    }
  }

  /**
   * 静态工厂方法：创建合并器
   */
  static createMerger(sourceProjectPath, targetProjectPath, options = {}) {
    return new PackageJsonMerger(sourceProjectPath, targetProjectPath, options);
  }

  /**
   * 静态工厂方法：创建升级器
   */
  static createUpgrader(projectPath, options = {}) {
    return new PackageJsonMerger({ projectPath, ...options });
  }
}

module.exports = PackageJsonMerger;
